<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music App - Refactored with Design System</title>
    <link rel="stylesheet" href="design-tokens.css">
    <link rel="stylesheet" href="components/ui/status-bar/status-bar.css">
    <link rel="stylesheet" href="components/ui/button/button.css">
    <link rel="stylesheet" href="components/ui/card/card.css">
    <link rel="stylesheet" href="components/ui/navigation/navigation.css">
    <link rel="stylesheet" href="components/ui/grid/grid.css">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: var(--color-background-primary);
            border-radius: var(--radius-4xl);
            padding: 0;
            position: relative;
            overflow: hidden;
            border: 3px solid var(--color-border-primary);
            display: flex;
            flex-direction: column;
        }

        .app-content {
            flex: 1;
            padding: 0 var(--spacing-xl);
            overflow-y: auto;
            padding-bottom: var(--spacing-xl);
        }

        .section-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .music-section {
            margin-bottom: var(--spacing-3xl);
        }

        .home-indicator {
            position: absolute;
            bottom: var(--spacing-sm);
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: var(--color-text-primary);
            border-radius: var(--radius-sm);
            opacity: 0.3;
        }

        /* Custom scrollbar */
        .app-content::-webkit-scrollbar {
            width: 2px;
        }

        .app-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .app-content::-webkit-scrollbar-thumb {
            background: var(--color-text-tertiary);
            border-radius: var(--radius-sm);
        }

        /* Animation for app loading */
        .phone-container {
            animation: phoneAppear 0.8s ease-out;
        }

        @keyframes phoneAppear {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Demo controls */
        .demo-controls {
            max-width: 600px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background: var(--color-surface-primary);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .demo-controls h3 {
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .control-group {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            flex-wrap: wrap;
        }

        .control-button {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--color-background-tertiary);
            color: var(--color-text-secondary);
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-all);
            font-size: var(--font-size-sm);
        }

        .control-button:hover {
            background: var(--color-background-quaternary);
            color: var(--color-text-primary);
        }

        .control-button.active {
            background: var(--color-primary);
            color: var(--color-text-primary);
        }

        /* Responsive */
        @media (max-width: 480px) {
            .phone-container {
                width: 100%;
                max-width: 375px;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                border: none;
            }

            .demo-controls {
                margin: var(--spacing-lg);
                padding: var(--spacing-lg);
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status Bar -->
        <div id="app-status-bar"></div>
        
        <!-- Navigation Tabs -->
        <div id="app-tab-navigation"></div>
        
        <!-- Main Content -->
        <div class="app-content">
            <!-- Music Grid Section -->
            <div class="music-section">
                <div id="music-grid-section"></div>
            </div>
            
            <!-- Picked for you Section -->
            <div class="music-section">
                <h2 class="section-title">Picked for you</h2>
                <div id="featured-section"></div>
            </div>
            
            <!-- New releases Section -->
            <div class="music-section">
                <h2 class="section-title">New releases for you</h2>
                <div id="releases-section"></div>
            </div>
        </div>
        
        <!-- Bottom Navigation -->
        <div id="app-bottom-navigation"></div>
        
        <!-- Home Indicator -->
        <div class="home-indicator"></div>
    </div>

    <!-- Demo Controls -->
    <div class="demo-controls">
        <h3>Interactive Demo Controls</h3>
        
        <div class="control-group">
            <button class="control-button" onclick="toggleTheme()">Toggle Theme</button>
            <button class="control-button" onclick="simulateLoading()">Simulate Loading</button>
            <button class="control-button" onclick="addNewItem()">Add Music Item</button>
            <button class="control-button" onclick="shuffleItems()">Shuffle Items</button>
        </div>
        
        <div class="control-group">
            <button class="control-button" onclick="changeBatteryLevel(20)">Low Battery</button>
            <button class="control-button" onclick="changeBatteryLevel(80)">Normal Battery</button>
            <button class="control-button" onclick="toggleCharging()">Toggle Charging</button>
        </div>
        
        <div class="control-group">
            <button class="control-button" onclick="changeGridColumns(1)">1 Column</button>
            <button class="control-button active" onclick="changeGridColumns(2)">2 Columns</button>
            <button class="control-button" onclick="changeGridColumns(3)">3 Columns</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="components/ui/status-bar/status-bar.js"></script>
    <script src="components/ui/button/button.js"></script>
    <script src="components/ui/card/card.js"></script>
    <script src="components/ui/navigation/navigation.js"></script>
    <script src="components/ui/grid/grid.js"></script>
    
    <script>
        // App state
        let appState = {
            isDarkTheme: true,
            isCharging: false,
            batteryLevel: 80,
            gridColumns: 2
        };

        // Component instances
        let statusBar, tabNavigation, musicGrid, featuredCard, releasesGrid, bottomNavigation;

        // Sample data
        const musicItems = [
            { title: 'Brat and it\'s completely diff...', icon: '🎵', color: '#4CAF50' },
            { title: 'Wicked Official Playlist', icon: '🎭', color: '#FF5722' },
            { title: 'Gracie Abrams', icon: '👤', color: '#9C27B0' },
            { title: 'More Life', icon: '🎸', color: '#FF9800' },
            { title: 'DJ', icon: '🎧', color: '#2196F3' },
            { title: 'Today\'s Top Hits', icon: '🎵', color: '#607D8B' },
            { title: 'eternal sunshine', icon: '☀️', color: '#795548' },
            { title: 'Short n\' Sweet', icon: '💋', color: '#E91E63' }
        ];

        const releaseItems = [
            { title: 'New Album', color: 'linear-gradient(45deg, #FFD700, #FFA500)' },
            { title: 'EP Release', color: 'linear-gradient(45deg, #00CED1, #1E90FF)' },
            { title: 'Single', color: 'linear-gradient(45deg, #DDA0DD, #9370DB)' },
            { title: 'Compilation', color: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)' }
        ];

        // Initialize app
        function initializeApp() {
            // Status Bar
            statusBar = new StatusBar({
                time: '9:41',
                batteryLevel: appState.batteryLevel,
                signalStrength: 'strong',
                wifiConnected: true,
                isCharging: appState.isCharging,
                autoUpdate: false
            }).mount('#app-status-bar');

            // Tab Navigation
            tabNavigation = new TabNavigation({
                tabs: [
                    { label: 'T' },
                    { label: 'All' },
                    { label: 'Music' },
                    { label: 'Podcasts' },
                    { label: 'Audiobooks' }
                ],
                activeTab: 1,
                onChange: (index, tab) => {
                    console.log('Tab changed:', tab.label);
                    // You could load different content based on the tab
                }
            }).mount('#app-tab-navigation');

            // Music Grid
            musicGrid = Grid.createMusicGrid(musicItems, {
                columns: appState.gridColumns,
                itemRenderer: (item, index) => {
                    return `
                        <div class="card card--music" onclick="playMusic('${item.title}')">
                            <div class="card__avatar" style="background: ${item.color};">
                                ${item.icon}
                            </div>
                            <h3 class="card__title">${item.title}</h3>
                        </div>
                    `;
                }
            }).mount('#music-grid-section');

            // Featured Card
            featuredCard = Card.createFeaturedCard({
                avatar: '🎙️',
                badge: 'Podcast',
                title: 'Sounds Like A Cult',
                description: 'A podcast about the modern-day "cults" we all follow.',
                backgroundColor: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                clickable: true,
                onClick: () => {
                    console.log('Featured content clicked');
                }
            }).mount('#featured-section');

            // Releases Grid
            releasesGrid = Grid.createReleaseGrid(releaseItems, {
                itemRenderer: (item, index) => {
                    return `
                        <div class="card card--release" onclick="viewRelease('${item.title}')">
                            <div class="card__image" style="background: ${item.color};"></div>
                            <h3 class="card__title">${item.title}</h3>
                        </div>
                    `;
                }
            }).mount('#releases-section');

            // Bottom Navigation
            bottomNavigation = new BottomNavigation({
                items: [
                    { icon: 'home', label: 'Home' },
                    { icon: 'search', label: 'Search' },
                    { icon: 'library', label: 'Your Library', badge: 3 },
                    { icon: 'create', label: 'Create' }
                ],
                activeItem: 0,
                position: 'relative',
                onChange: (index, item) => {
                    console.log('Navigation changed:', item.label);
                    // Handle navigation changes
                }
            }).mount('#app-bottom-navigation');
        }

        // Demo functions
        function toggleTheme() {
            appState.isDarkTheme = !appState.isDarkTheme;
            // In a real app, you would toggle CSS classes or variables
            console.log('Theme toggled:', appState.isDarkTheme ? 'Dark' : 'Light');
        }

        function simulateLoading() {
            musicGrid.setLoading(true);
            setTimeout(() => {
                musicGrid.setLoading(false);
            }, 2000);
        }

        function addNewItem() {
            const newItem = {
                title: `New Song ${Date.now()}`,
                icon: '🎶',
                color: '#' + Math.floor(Math.random()*16777215).toString(16)
            };
            musicGrid.addItem(newItem);
        }

        function shuffleItems() {
            const shuffled = [...musicItems].sort(() => Math.random() - 0.5);
            musicGrid.setItems(shuffled);
        }

        function changeBatteryLevel(level) {
            appState.batteryLevel = level;
            statusBar.updateBattery(level, appState.isCharging);
        }

        function toggleCharging() {
            appState.isCharging = !appState.isCharging;
            statusBar.updateBattery(appState.batteryLevel, appState.isCharging);
        }

        function changeGridColumns(columns) {
            appState.gridColumns = columns;
            musicGrid.setColumns(columns);
            
            // Update button states
            document.querySelectorAll('.control-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Interaction handlers
        function playMusic(title) {
            console.log('Playing:', title);
            // In a real app, you would start music playback
        }

        function viewRelease(title) {
            console.log('Viewing release:', title);
            // In a real app, you would navigate to the release page
        }

        // Initialize the app when the page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
