/**
 * Design System Components Index
 * 设计系统组件统一入口文件
 * 
 * 这个文件提供了所有组件的统一导入和初始化方法
 */

// Component imports (for module systems)
let StatusBar, Button, Card, TabNavigation, BottomNavigation, Breadcrumb, Grid, GridSection;

// Check if we're in a module environment
if (typeof module !== 'undefined' && module.exports) {
    StatusBar = require('./ui/status-bar/status-bar.js');
    Button = require('./ui/button/button.js');
    Card = require('./ui/card/card.js');
    const NavigationComponents = require('./ui/navigation/navigation.js');
    TabNavigation = NavigationComponents.TabNavigation;
    BottomNavigation = NavigationComponents.BottomNavigation;
    Breadcrumb = NavigationComponents.Breadcrumb;
    const GridComponents = require('./ui/grid/grid.js');
    Grid = GridComponents.Grid;
    GridSection = GridComponents.GridSection;
} else {
    // Browser environment - components should be loaded via script tags
    StatusBar = window.StatusBar;
    Button = window.Button;
    Card = window.Card;
    TabNavigation = window.TabNavigation;
    BottomNavigation = window.BottomNavigation;
    Breadcrumb = window.Breadcrumb;
    Grid = window.Grid;
    GridSection = window.GridSection;
}

/**
 * Design System Factory
 * 设计系统工厂类，提供便捷的组件创建方法
 */
class DesignSystem {
    constructor() {
        this.components = new Map();
        this.themes = new Map();
        this.currentTheme = 'default';
    }

    // Component factory methods
    createStatusBar(options = {}) {
        const component = new StatusBar(options);
        this.registerComponent('status-bar', component);
        return component;
    }

    createButton(options = {}) {
        const component = new Button(options);
        this.registerComponent('button', component);
        return component;
    }

    createCard(options = {}) {
        const component = new Card(options);
        this.registerComponent('card', component);
        return component;
    }

    createTabNavigation(options = {}) {
        const component = new TabNavigation(options);
        this.registerComponent('tab-navigation', component);
        return component;
    }

    createBottomNavigation(options = {}) {
        const component = new BottomNavigation(options);
        this.registerComponent('bottom-navigation', component);
        return component;
    }

    createBreadcrumb(options = {}) {
        const component = new Breadcrumb(options);
        this.registerComponent('breadcrumb', component);
        return component;
    }

    createGrid(options = {}) {
        const component = new Grid(options);
        this.registerComponent('grid', component);
        return component;
    }

    createGridSection(options = {}) {
        const component = new GridSection(options);
        this.registerComponent('grid-section', component);
        return component;
    }

    // Specialized factory methods
    createMusicCard(options = {}) {
        return this.createCard({ ...options, variant: 'music', clickable: true });
    }

    createFeaturedCard(options = {}) {
        return this.createCard({ ...options, variant: 'featured' });
    }

    createReleaseCard(options = {}) {
        return this.createCard({ ...options, variant: 'release' });
    }

    createMusicGrid(items, options = {}) {
        return this.createGrid({ ...options, type: 'music', items });
    }

    createReleaseGrid(items, options = {}) {
        return this.createGrid({ ...options, type: 'release', items });
    }

    createIconButton(icon, onClick, ariaLabel) {
        return this.createButton({
            icon,
            iconOnly: true,
            variant: 'ghost',
            onClick,
            ariaLabel
        });
    }

    // Component management
    registerComponent(id, component) {
        this.components.set(id, component);
    }

    getComponent(id) {
        return this.components.get(id);
    }

    getAllComponents() {
        return Array.from(this.components.values());
    }

    destroyComponent(id) {
        const component = this.components.get(id);
        if (component && typeof component.destroy === 'function') {
            component.destroy();
            this.components.delete(id);
        }
    }

    destroyAllComponents() {
        this.components.forEach((component, id) => {
            if (typeof component.destroy === 'function') {
                component.destroy();
            }
        });
        this.components.clear();
    }

    // Theme management
    registerTheme(name, theme) {
        this.themes.set(name, theme);
    }

    setTheme(name) {
        const theme = this.themes.get(name);
        if (theme) {
            this.currentTheme = name;
            this.applyTheme(theme);
        }
    }

    applyTheme(theme) {
        const root = document.documentElement;
        Object.entries(theme).forEach(([property, value]) => {
            root.style.setProperty(property, value);
        });
    }

    // Utility methods
    loadCSS(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    async loadComponentStyles(components = []) {
        const baseUrl = './components/ui/';
        const promises = components.map(component => 
            this.loadCSS(`${baseUrl}${component}/${component}.css`)
        );
        
        try {
            await Promise.all(promises);
            console.log('All component styles loaded successfully');
        } catch (error) {
            console.error('Error loading component styles:', error);
        }
    }

    // App builder helpers
    buildMusicApp(container, options = {}) {
        const app = {
            statusBar: null,
            tabNavigation: null,
            musicGrid: null,
            featuredCard: null,
            releasesGrid: null,
            bottomNavigation: null
        };

        // Default music app data
        const defaultData = {
            musicItems: [
                { title: 'Brat and it\'s completely diff...', icon: '🎵', color: '#4CAF50' },
                { title: 'Wicked Official Playlist', icon: '🎭', color: '#FF5722' },
                { title: 'Gracie Abrams', icon: '👤', color: '#9C27B0' },
                { title: 'More Life', icon: '🎸', color: '#FF9800' }
            ],
            releaseItems: [
                { title: 'New Album', color: 'linear-gradient(45deg, #FFD700, #FFA500)' },
                { title: 'EP Release', color: 'linear-gradient(45deg, #00CED1, #1E90FF)' },
                { title: 'Single', color: 'linear-gradient(45deg, #DDA0DD, #9370DB)' }
            ],
            featuredContent: {
                avatar: '🎙️',
                badge: 'Podcast',
                title: 'Sounds Like A Cult',
                description: 'A podcast about the modern-day "cults" we all follow.',
                backgroundColor: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)'
            }
        };

        const data = { ...defaultData, ...options.data };

        // Create app structure
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }

        container.innerHTML = `
            <div class="music-app">
                <div id="app-status-bar"></div>
                <div id="app-tab-nav"></div>
                <div class="app-content">
                    <div id="app-music-grid"></div>
                    <div class="section">
                        <h2 class="section-title">Picked for you</h2>
                        <div id="app-featured"></div>
                    </div>
                    <div class="section">
                        <h2 class="section-title">New releases for you</h2>
                        <div id="app-releases"></div>
                    </div>
                </div>
                <div id="app-bottom-nav"></div>
            </div>
        `;

        // Initialize components
        app.statusBar = this.createStatusBar({
            time: '9:41',
            batteryLevel: 80,
            signalStrength: 'strong',
            wifiConnected: true,
            autoUpdate: false
        }).mount('#app-status-bar');

        app.tabNavigation = this.createTabNavigation({
            tabs: ['T', 'All', 'Music', 'Podcasts', 'Audiobooks'],
            activeTab: 1
        }).mount('#app-tab-nav');

        app.musicGrid = this.createMusicGrid(data.musicItems, {
            columns: 2,
            itemRenderer: (item, index) => `
                <div class="card card--music">
                    <div class="card__avatar" style="background: ${item.color};">
                        ${item.icon}
                    </div>
                    <h3 class="card__title">${item.title}</h3>
                </div>
            `
        }).mount('#app-music-grid');

        app.featuredCard = this.createFeaturedCard(data.featuredContent)
            .mount('#app-featured');

        app.releasesGrid = this.createReleaseGrid(data.releaseItems, {
            itemRenderer: (item, index) => `
                <div class="card card--release">
                    <div class="card__image" style="background: ${item.color};"></div>
                    <h3 class="card__title">${item.title}</h3>
                </div>
            `
        }).mount('#app-releases');

        app.bottomNavigation = this.createBottomNavigation({
            items: [
                { icon: 'home', label: 'Home' },
                { icon: 'search', label: 'Search' },
                { icon: 'library', label: 'Your Library' },
                { icon: 'create', label: 'Create' }
            ],
            activeItem: 0,
            position: 'relative'
        }).mount('#app-bottom-nav');

        return app;
    }
}

// Create global instance
const designSystem = new DesignSystem();

// Register default themes
designSystem.registerTheme('dark', {
    '--color-background-primary': '#000000',
    '--color-background-secondary': '#1c1c1e',
    '--color-text-primary': '#ffffff',
    '--color-text-secondary': '#999999'
});

designSystem.registerTheme('light', {
    '--color-background-primary': '#ffffff',
    '--color-background-secondary': '#f2f2f7',
    '--color-text-primary': '#000000',
    '--color-text-secondary': '#666666'
});

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DesignSystem,
        designSystem,
        StatusBar,
        Button,
        Card,
        TabNavigation,
        BottomNavigation,
        Breadcrumb,
        Grid,
        GridSection
    };
} else {
    // Browser environment
    window.DesignSystem = DesignSystem;
    window.designSystem = designSystem;
}

// Auto-initialize if in browser
if (typeof window !== 'undefined') {
    // Make components available globally for convenience
    window.DS = designSystem;
    
    console.log('🎨 Design System loaded successfully!');
    console.log('Use window.DS or window.designSystem to access the design system');
}
