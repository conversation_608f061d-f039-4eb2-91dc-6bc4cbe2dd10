/**
 * Card Component
 * 卡片组件 - 支持多种样式和布局
 */
class Card {
  constructor(options = {}) {
    this.options = {
      variant: options.variant || 'default', // default, elevated, outlined, filled, glass, music, featured, release
      size: options.size || 'md', // xs, sm, md, lg, xl
      clickable: options.clickable || false,
      loading: options.loading || false,
      
      // Header options
      avatar: options.avatar || null,
      avatarSize: options.avatarSize || 'md',
      title: options.title || '',
      subtitle: options.subtitle || '',
      headerAction: options.headerAction || null,
      showHeaderBorder: options.showHeaderBorder !== false,
      
      // Body options
      content: options.content || '',
      image: options.image || null,
      imageAspect: options.imageAspect || null, // square, video, portrait
      
      // Footer options
      footerContent: options.footerContent || '',
      footerActions: options.footerActions || [],
      showFooterBorder: options.showFooterBorder !== false,
      
      // Music card specific
      icon: options.icon || null,
      backgroundColor: options.backgroundColor || null,
      
      // Featured card specific
      badge: options.badge || '',
      description: options.description || '',
      
      // Events
      onClick: options.onClick || null,
      
      ...options
    };
    
    this.element = null;
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('div');
    this.updateClasses();
    this.updateAttributes();
  }
  
  updateClasses() {
    const { variant, size, clickable, loading } = this.options;
    
    const classes = [
      'card',
      variant !== 'default' && `card--${variant}`,
      size !== 'md' && `card--${size}`,
      clickable && 'card--clickable',
      loading && 'card--loading'
    ].filter(Boolean);
    
    this.element.className = classes.join(' ');
  }
  
  updateAttributes() {
    const { clickable, title } = this.options;
    
    if (clickable) {
      this.element.setAttribute('role', 'button');
      this.element.setAttribute('tabindex', '0');
      if (title) {
        this.element.setAttribute('aria-label', title);
      }
    }
  }
  
  render() {
    const { variant } = this.options;
    
    switch (variant) {
      case 'music':
        this.renderMusicCard();
        break;
      case 'featured':
        this.renderFeaturedCard();
        break;
      case 'release':
        this.renderReleaseCard();
        break;
      default:
        this.renderDefaultCard();
        break;
    }
  }
  
  renderDefaultCard() {
    const { image, title, subtitle, content, avatar, headerAction, footerContent, footerActions, showHeaderBorder, showFooterBorder } = this.options;
    
    let html = '';
    
    // Image
    if (image) {
      html += this.renderImage();
    }
    
    // Header
    if (title || subtitle || avatar || headerAction) {
      html += this.renderHeader(showHeaderBorder);
    }
    
    // Body
    if (content) {
      html += `<div class="card__body">${content}</div>`;
    }
    
    // Footer
    if (footerContent || footerActions.length > 0) {
      html += this.renderFooter(showFooterBorder);
    }
    
    this.element.innerHTML = html;
  }
  
  renderMusicCard() {
    const { icon, title, subtitle, backgroundColor } = this.options;
    
    const avatarStyle = backgroundColor ? `style="background: ${backgroundColor};"` : '';
    const iconContent = typeof icon === 'string' ? icon : '🎵';
    
    this.element.innerHTML = `
      <div class="card__avatar" ${avatarStyle}>
        ${iconContent}
      </div>
      <h3 class="card__title">${title}</h3>
      ${subtitle ? `<p class="card__subtitle">${subtitle}</p>` : ''}
    `;
  }
  
  renderFeaturedCard() {
    const { avatar, badge, title, description, backgroundColor } = this.options;
    
    const avatarStyle = backgroundColor ? `style="background: ${backgroundColor};"` : '';
    const avatarContent = typeof avatar === 'string' ? avatar : '🎙️';
    
    this.element.innerHTML = `
      <div class="card__avatar" ${avatarStyle}>
        ${avatarContent}
      </div>
      <div class="card__content">
        ${badge ? `<p class="card__badge">${badge}</p>` : ''}
        <h3 class="card__title">${title}</h3>
        ${description ? `<p class="card__description">${description}</p>` : ''}
      </div>
    `;
  }
  
  renderReleaseCard() {
    const { title, backgroundColor } = this.options;
    
    const imageStyle = backgroundColor ? `style="background: ${backgroundColor};"` : '';
    
    this.element.innerHTML = `
      <div class="card__image" ${imageStyle}></div>
      ${title ? `<h3 class="card__title">${title}</h3>` : ''}
    `;
  }
  
  renderHeader(showBorder = true) {
    const { avatar, avatarSize, title, subtitle, headerAction } = this.options;
    
    const borderClass = showBorder ? '' : 'card__header--no-border';
    
    return `
      <div class="card__header ${borderClass}">
        ${avatar ? this.renderAvatar(avatar, avatarSize) : ''}
        <div class="card__header-content">
          ${title ? `<h3 class="card__title">${title}</h3>` : ''}
          ${subtitle ? `<p class="card__subtitle">${subtitle}</p>` : ''}
        </div>
        ${headerAction ? `<div class="card__header-action">${headerAction}</div>` : ''}
      </div>
    `;
  }
  
  renderAvatar(avatar, size = 'md') {
    const sizeClass = size !== 'md' ? `card__avatar--${size}` : '';
    
    if (typeof avatar === 'string') {
      if (avatar.startsWith('http') || avatar.startsWith('/')) {
        // Image URL
        return `<div class="card__avatar ${sizeClass}"><img src="${avatar}" alt="Avatar"></div>`;
      } else {
        // Text or emoji
        return `<div class="card__avatar ${sizeClass}">${avatar}</div>`;
      }
    }
    
    return `<div class="card__avatar ${sizeClass}"></div>`;
  }
  
  renderImage() {
    const { image, imageAspect } = this.options;
    
    const aspectClass = imageAspect ? `card__image--aspect-${imageAspect}` : '';
    
    if (typeof image === 'string') {
      return `
        <div class="card__image ${aspectClass}">
          <img src="${image}" alt="">
        </div>
      `;
    }
    
    return '';
  }
  
  renderFooter(showBorder = true) {
    const { footerContent, footerActions } = this.options;
    
    const borderClass = showBorder ? '' : 'card__footer--no-border';
    
    return `
      <div class="card__footer ${borderClass}">
        ${footerContent ? `<div class="card__footer-content">${footerContent}</div>` : ''}
        ${footerActions.length > 0 ? `<div class="card__footer-actions">${footerActions.join('')}</div>` : ''}
      </div>
    `;
  }
  
  // Public methods
  setTitle(title) {
    this.options.title = title;
    this.render();
  }
  
  setSubtitle(subtitle) {
    this.options.subtitle = subtitle;
    this.render();
  }
  
  setContent(content) {
    this.options.content = content;
    this.render();
  }
  
  setLoading(loading) {
    this.options.loading = loading;
    this.updateClasses();
  }
  
  setClickable(clickable) {
    this.options.clickable = clickable;
    this.updateClasses();
    this.updateAttributes();
    if (clickable) {
      this.bindEvents();
    }
  }
  
  // Event handling
  bindEvents() {
    if (this.options.clickable && this.options.onClick) {
      this.element.addEventListener('click', this.handleClick.bind(this));
      this.element.addEventListener('keydown', this.handleKeydown.bind(this));
    }
  }
  
  handleClick(event) {
    if (this.options.onClick) {
      this.options.onClick(event, this);
    }
  }
  
  handleKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.handleClick(event);
    }
  }
  
  // Lifecycle methods
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
  
  destroy() {
    this.unmount();
    this.element = null;
    this.options = null;
  }
  
  // Static factory methods
  static createMusicCard(options) {
    return new Card({ ...options, variant: 'music', clickable: true });
  }
  
  static createFeaturedCard(options) {
    return new Card({ ...options, variant: 'featured' });
  }
  
  static createReleaseCard(options) {
    return new Card({ ...options, variant: 'release' });
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Card;
}

// Global registration
if (typeof window !== 'undefined') {
  window.Card = Card;
}
