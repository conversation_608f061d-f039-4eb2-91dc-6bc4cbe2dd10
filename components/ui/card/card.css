/* Card Component Styles */
.card {
  background: var(--color-surface-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  position: relative;
  overflow: hidden;
  transition: var(--transition-all);
  border: 1px solid transparent;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card--clickable {
  cursor: pointer;
}

.card--clickable:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

.card--clickable:active {
  transform: scale(0.98);
}

/* Card Variants */
.card--elevated {
  box-shadow: var(--shadow-sm);
}

.card--elevated:hover {
  box-shadow: var(--shadow-lg);
}

.card--outlined {
  border: 1px solid var(--color-border-primary);
  background: transparent;
}

.card--filled {
  background: var(--color-surface-secondary);
}

.card--glass {
  background: rgba(28, 28, 30, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Card Sizes */
.card--xs {
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.card--sm {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.card--md {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
}

.card--lg {
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

.card--xl {
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
}

/* Card Header */
.card__header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-secondary);
}

.card__header--no-border {
  border-bottom: none;
  padding-bottom: 0;
}

.card__avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: var(--color-background-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  flex-shrink: 0;
  overflow: hidden;
}

.card__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card__avatar--sm {
  width: 32px;
  height: 32px;
  font-size: var(--font-size-xs);
}

.card__avatar--lg {
  width: 48px;
  height: 48px;
  font-size: var(--font-size-base);
}

.card__avatar--xl {
  width: 80px;
  height: 80px;
  font-size: var(--font-size-lg);
}

.card__header-content {
  flex: 1;
  min-width: 0;
}

.card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: var(--line-height-tight);
}

.card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.card__header-action {
  flex-shrink: 0;
}

/* Card Body */
.card__body {
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
}

.card__body p {
  margin: 0 0 var(--spacing-sm) 0;
}

.card__body p:last-child {
  margin-bottom: 0;
}

/* Card Footer */
.card__footer {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--color-border-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.card__footer--no-border {
  border-top: none;
  padding-top: 0;
}

.card__footer-content {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.card__footer-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

/* Card Image */
.card__image {
  width: calc(100% + 2 * var(--spacing-md));
  margin: calc(-1 * var(--spacing-md)) calc(-1 * var(--spacing-md)) var(--spacing-md);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  overflow: hidden;
}

.card__image img {
  width: 100%;
  height: auto;
  display: block;
}

.card__image--aspect-square img {
  aspect-ratio: 1;
  object-fit: cover;
}

.card__image--aspect-video img {
  aspect-ratio: 16/9;
  object-fit: cover;
}

.card__image--aspect-portrait img {
  aspect-ratio: 3/4;
  object-fit: cover;
}

/* Music Card Specific Styles */
.card--music {
  background: var(--color-surface-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: var(--transition-all);
}

.card--music:hover {
  transform: scale(1.02);
}

.card--music .card__avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(45deg, var(--color-accent-blue), var(--color-accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

.card--music .card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

.card--music .card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-tight);
}

/* Featured Card */
.card--featured {
  background: var(--color-surface-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
}

.card--featured .card__avatar {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.card--featured .card__content {
  flex: 1;
}

.card--featured .card__badge {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.card--featured .card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.card--featured .card__description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* Release Card */
.card--release {
  min-width: 120px;
  text-align: center;
  background: transparent;
  padding: 0;
}

.card--release .card__image {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-md);
  margin: 0 0 var(--spacing-sm) 0;
  background: linear-gradient(45deg, var(--color-accent-orange), var(--color-accent-pink));
}

.card--release .card__title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

/* Loading state */
.card--loading {
  pointer-events: none;
}

.card--loading .card__avatar,
.card--loading .card__title,
.card--loading .card__subtitle,
.card--loading .card__body {
  background: linear-gradient(90deg, 
    var(--color-background-tertiary) 25%, 
    var(--color-background-quaternary) 50%, 
    var(--color-background-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: cardLoading 1.5s infinite;
  border-radius: var(--radius-sm);
  color: transparent;
}

.card--loading .card__title {
  height: 16px;
  margin-bottom: var(--spacing-xs);
}

.card--loading .card__subtitle {
  height: 14px;
  width: 60%;
}

@keyframes cardLoading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Accessibility */
.card--clickable:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .card {
    transition: none;
  }
  
  .card--loading .card__avatar,
  .card--loading .card__title,
  .card--loading .card__subtitle,
  .card--loading .card__body {
    animation: none;
  }
}

/* Responsive */
@media (max-width: 480px) {
  .card--featured {
    flex-direction: column;
    text-align: center;
  }
  
  .card--featured .card__avatar {
    align-self: center;
  }
}
