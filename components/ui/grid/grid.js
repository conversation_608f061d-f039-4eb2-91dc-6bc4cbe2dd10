/**
 * Grid Component
 * 网格组件 - 支持响应式布局和多种网格类型
 */
class Grid {
  constructor(options = {}) {
    this.options = {
      columns: options.columns || 2,
      gap: options.gap || 'lg',
      type: options.type || 'default', // default, music, release, masonry
      responsive: options.responsive !== false,
      loading: options.loading || false,
      stagger: options.stagger || false,
      items: options.items || [],
      itemRenderer: options.itemRenderer || null,
      container: options.container || 'default', // default, full, narrow, wide
      ...options
    };
    
    this.element = null;
    this.items = [...this.options.items];
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('div');
    this.updateClasses();
  }
  
  updateClasses() {
    const { type, columns, gap, loading, stagger, responsive } = this.options;
    
    const classes = [
      type === 'music' ? 'music-grid' :
      type === 'release' ? 'release-grid' :
      type === 'masonry' ? 'masonry-grid' : 'grid',
      
      type === 'default' && `grid--cols-${columns}`,
      type === 'music' && columns !== 2 && `music-grid--${columns}-cols`,
      type === 'masonry' && columns !== 2 && `masonry-grid--${columns}-cols`,
      
      gap !== 'lg' && `grid--gap-${gap}`,
      loading && 'grid--loading',
      stagger && 'grid--stagger'
    ].filter(Boolean);
    
    this.element.className = classes.join(' ');
  }
  
  render() {
    if (this.options.loading) {
      this.renderLoadingState();
    } else {
      this.renderItems();
    }
  }
  
  renderLoadingState() {
    const { columns } = this.options;
    const loadingItems = Array.from({ length: columns * 2 }, (_, index) => 
      `<div class="grid-item" key="loading-${index}"></div>`
    );
    
    this.element.innerHTML = loadingItems.join('');
  }
  
  renderItems() {
    const { itemRenderer } = this.options;
    
    if (itemRenderer && typeof itemRenderer === 'function') {
      const itemsHtml = this.items.map((item, index) => {
        const itemHtml = itemRenderer(item, index);
        return `<div class="grid-item" data-index="${index}">${itemHtml}</div>`;
      });
      
      this.element.innerHTML = itemsHtml.join('');
    } else {
      // Default rendering for simple items
      const itemsHtml = this.items.map((item, index) => {
        const content = typeof item === 'string' ? item : 
                       item.content || item.title || JSON.stringify(item);
        return `<div class="grid-item" data-index="${index}">${content}</div>`;
      });
      
      this.element.innerHTML = itemsHtml.join('');
    }
    
    // Add animation classes if stagger is enabled
    if (this.options.stagger) {
      this.addStaggerAnimation();
    }
  }
  
  addStaggerAnimation() {
    const items = this.element.querySelectorAll('.grid-item');
    items.forEach((item, index) => {
      setTimeout(() => {
        item.classList.add('grid-item--animate-in');
      }, index * 100);
    });
  }
  
  // Public methods
  setColumns(columns) {
    this.options.columns = columns;
    this.updateClasses();
  }
  
  setGap(gap) {
    this.options.gap = gap;
    this.updateClasses();
  }
  
  setLoading(loading) {
    this.options.loading = loading;
    this.updateClasses();
    this.render();
  }
  
  addItem(item, index = -1) {
    if (index === -1) {
      this.items.push(item);
    } else {
      this.items.splice(index, 0, item);
    }
    this.render();
  }
  
  removeItem(index) {
    if (index >= 0 && index < this.items.length) {
      const item = this.element.querySelector(`[data-index="${index}"]`);
      if (item) {
        item.classList.add('grid-item--animate-out');
        setTimeout(() => {
          this.items.splice(index, 1);
          this.render();
        }, 300);
      }
    }
  }
  
  updateItem(index, newItem) {
    if (index >= 0 && index < this.items.length) {
      this.items[index] = newItem;
      this.render();
    }
  }
  
  setItems(items) {
    this.items = [...items];
    this.render();
  }
  
  getItems() {
    return [...this.items];
  }
  
  clear() {
    this.items = [];
    this.render();
  }
  
  // Layout methods
  setResponsive(responsive) {
    this.options.responsive = responsive;
    // Add or remove responsive classes as needed
  }
  
  refresh() {
    this.render();
  }
  
  // Event handling
  bindEvents() {
    // Handle responsive changes
    if (this.options.responsive) {
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener('resize', this.handleResize);
    }
    
    // Handle item clicks if needed
    this.element.addEventListener('click', this.handleItemClick.bind(this));
  }
  
  handleResize() {
    // Debounce resize events
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.updateResponsiveLayout();
    }, 150);
  }
  
  updateResponsiveLayout() {
    const width = window.innerWidth;
    const { type, columns } = this.options;
    
    // Auto-adjust columns based on screen size
    if (type === 'music') {
      if (width <= 480) {
        this.element.className = this.element.className.replace(/music-grid--\d+-cols/, '');
      } else if (width <= 768) {
        this.setColumns(2);
      } else {
        this.setColumns(columns);
      }
    }
  }
  
  handleItemClick(event) {
    const item = event.target.closest('.grid-item');
    if (!item) return;
    
    const index = parseInt(item.dataset.index);
    if (isNaN(index)) return;
    
    // Emit custom event
    const customEvent = new CustomEvent('gridItemClick', {
      detail: {
        index,
        item: this.items[index],
        element: item
      }
    });
    
    this.element.dispatchEvent(customEvent);
  }
  
  // Lifecycle methods
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      // Wrap in container if specified
      if (this.options.container !== 'default') {
        const wrapper = document.createElement('div');
        wrapper.className = `grid-container grid-container--${this.options.container}`;
        wrapper.appendChild(this.element);
        container.appendChild(wrapper);
      } else {
        container.appendChild(this.element);
      }
    }
    
    return this;
  }
  
  unmount() {
    if (this.options.responsive) {
      window.removeEventListener('resize', this.handleResize);
    }
    
    const parent = this.element.parentNode;
    if (parent) {
      // Remove wrapper if it exists
      if (parent.classList.contains('grid-container')) {
        parent.parentNode.removeChild(parent);
      } else {
        parent.removeChild(this.element);
      }
    }
  }
  
  destroy() {
    this.unmount();
    this.element = null;
    this.items = null;
    this.options = null;
  }
  
  // Static factory methods
  static createMusicGrid(items, options = {}) {
    return new Grid({
      ...options,
      type: 'music',
      items,
      columns: options.columns || 2
    });
  }
  
  static createReleaseGrid(items, options = {}) {
    return new Grid({
      ...options,
      type: 'release',
      items
    });
  }
  
  static createMasonryGrid(items, options = {}) {
    return new Grid({
      ...options,
      type: 'masonry',
      items,
      columns: options.columns || 3
    });
  }
}

// Grid Section Component
class GridSection {
  constructor(options = {}) {
    this.options = {
      title: options.title || '',
      subtitle: options.subtitle || '',
      actions: options.actions || [],
      grid: options.grid || null,
      ...options
    };
    
    this.element = null;
    this.grid = this.options.grid;
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
  }
  
  createElement() {
    this.element = document.createElement('section');
    this.element.className = 'grid-section';
  }
  
  render() {
    const { title, subtitle, actions } = this.options;
    
    const header = (title || subtitle || actions.length > 0) ? `
      <div class="grid-section__header">
        ${title ? `<h2 class="grid-section__title">${title}</h2>` : ''}
        ${subtitle ? `<p class="grid-section__subtitle">${subtitle}</p>` : ''}
        ${actions.length > 0 ? `<div class="grid-section__actions">${actions.join('')}</div>` : ''}
      </div>
    ` : '';
    
    this.element.innerHTML = header;
    
    if (this.grid && this.grid.element) {
      this.element.appendChild(this.grid.element);
    }
  }
  
  setTitle(title) {
    this.options.title = title;
    this.render();
  }
  
  setSubtitle(subtitle) {
    this.options.subtitle = subtitle;
    this.render();
  }
  
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  destroy() {
    if (this.grid) {
      this.grid.destroy();
    }
    
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    
    this.element = null;
    this.grid = null;
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { Grid, GridSection };
}

// Global registration
if (typeof window !== 'undefined') {
  window.Grid = Grid;
  window.GridSection = GridSection;
}
