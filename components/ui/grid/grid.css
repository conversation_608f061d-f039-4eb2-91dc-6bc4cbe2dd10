/* Grid Component Styles */
.grid {
  display: grid;
  gap: var(--spacing-lg);
  width: 100%;
}

/* Grid Columns */
.grid--cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid--cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid--cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid--cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid--cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid--cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid--cols-12 { grid-template-columns: repeat(12, 1fr); }

/* Auto-fit columns */
.grid--auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid--auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Grid Gaps */
.grid--gap-xs { gap: var(--spacing-xs); }
.grid--gap-sm { gap: var(--spacing-sm); }
.grid--gap-md { gap: var(--spacing-md); }
.grid--gap-lg { gap: var(--spacing-lg); }
.grid--gap-xl { gap: var(--spacing-xl); }
.grid--gap-2xl { gap: var(--spacing-2xl); }

/* Grid Item Spans */
.grid-item--span-1 { grid-column: span 1; }
.grid-item--span-2 { grid-column: span 2; }
.grid-item--span-3 { grid-column: span 3; }
.grid-item--span-4 { grid-column: span 4; }
.grid-item--span-5 { grid-column: span 5; }
.grid-item--span-6 { grid-column: span 6; }
.grid-item--span-full { grid-column: 1 / -1; }

/* Row spans */
.grid-item--row-span-1 { grid-row: span 1; }
.grid-item--row-span-2 { grid-row: span 2; }
.grid-item--row-span-3 { grid-row: span 3; }
.grid-item--row-span-4 { grid-row: span 4; }

/* Grid Alignment */
.grid--items-start { align-items: start; }
.grid--items-center { align-items: center; }
.grid--items-end { align-items: end; }
.grid--items-stretch { align-items: stretch; }

.grid--justify-start { justify-items: start; }
.grid--justify-center { justify-items: center; }
.grid--justify-end { justify-items: end; }
.grid--justify-stretch { justify-items: stretch; }

.grid--content-start { align-content: start; }
.grid--content-center { align-content: center; }
.grid--content-end { align-content: end; }
.grid--content-between { align-content: space-between; }
.grid--content-around { align-content: space-around; }
.grid--content-evenly { align-content: space-evenly; }

/* Music Grid Specific */
.music-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
}

.music-grid--3-cols {
  grid-template-columns: repeat(3, 1fr);
}

.music-grid--4-cols {
  grid-template-columns: repeat(4, 1fr);
}

/* Release Grid */
.release-grid {
  display: flex;
  gap: var(--spacing-sm);
  overflow-x: auto;
  padding-bottom: var(--spacing-xl);
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.release-grid::-webkit-scrollbar {
  display: none;
}

.release-grid__item {
  flex-shrink: 0;
}

/* Masonry Grid */
.masonry-grid {
  columns: 2;
  column-gap: var(--spacing-lg);
  column-fill: balance;
}

.masonry-grid--3-cols {
  columns: 3;
}

.masonry-grid--4-cols {
  columns: 4;
}

.masonry-grid__item {
  break-inside: avoid;
  margin-bottom: var(--spacing-lg);
  display: inline-block;
  width: 100%;
}

/* Responsive Grid */
@media (max-width: 768px) {
  .grid--cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid--cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid--cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .music-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .music-grid--3-cols,
  .music-grid--4-cols {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .masonry-grid {
    columns: 1;
  }
  
  .masonry-grid--3-cols,
  .masonry-grid--4-cols {
    columns: 2;
  }
}

@media (max-width: 480px) {
  .grid--cols-2 {
    grid-template-columns: 1fr;
  }
  
  .grid--cols-3,
  .grid--cols-4,
  .grid--cols-6 {
    grid-template-columns: 1fr;
  }
  
  .music-grid {
    grid-template-columns: 1fr;
  }
  
  .music-grid--3-cols,
  .music-grid--4-cols {
    grid-template-columns: 1fr;
  }
  
  .masonry-grid,
  .masonry-grid--3-cols,
  .masonry-grid--4-cols {
    columns: 1;
  }
}

/* Grid Container */
.grid-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.grid-container--full {
  max-width: none;
  padding: 0;
}

.grid-container--narrow {
  max-width: 600px;
}

.grid-container--wide {
  max-width: 1200px;
}

/* Grid Section */
.grid-section {
  margin-bottom: var(--spacing-3xl);
}

.grid-section__header {
  margin-bottom: var(--spacing-xl);
}

.grid-section__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.grid-section__subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.grid-section__actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

/* Loading Grid */
.grid--loading .grid-item {
  background: linear-gradient(90deg, 
    var(--color-background-tertiary) 25%, 
    var(--color-background-quaternary) 50%, 
    var(--color-background-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: gridLoading 1.5s infinite;
  border-radius: var(--radius-lg);
  min-height: 120px;
}

@keyframes gridLoading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Grid Item Animations */
.grid-item {
  transition: var(--transition-all);
}

.grid-item--animate-in {
  animation: gridItemFadeIn 0.3s ease-out;
}

@keyframes gridItemFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grid-item--animate-out {
  animation: gridItemFadeOut 0.3s ease-in;
}

@keyframes gridItemFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Staggered animations */
.grid--stagger .grid-item:nth-child(1) { animation-delay: 0ms; }
.grid--stagger .grid-item:nth-child(2) { animation-delay: 100ms; }
.grid--stagger .grid-item:nth-child(3) { animation-delay: 200ms; }
.grid--stagger .grid-item:nth-child(4) { animation-delay: 300ms; }
.grid--stagger .grid-item:nth-child(5) { animation-delay: 400ms; }
.grid--stagger .grid-item:nth-child(6) { animation-delay: 500ms; }

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .grid-item,
  .grid--loading .grid-item {
    animation: none;
    transition: none;
  }
}

/* Print styles */
@media print {
  .grid {
    display: block;
  }
  
  .grid-item {
    break-inside: avoid;
    margin-bottom: var(--spacing-lg);
  }
}
