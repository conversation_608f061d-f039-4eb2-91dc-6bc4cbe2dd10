/* Button Component Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-xl);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
  min-height: 44px; /* iOS touch target minimum */
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary);
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.btn:active {
  transform: scale(0.98);
}

/* Button Variants */
.btn--primary {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.btn--primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
}

.btn--primary:active:not(:disabled) {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-background-tertiary);
  color: var(--color-text-secondary);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-background-quaternary);
  color: var(--color-text-primary);
}

.btn--secondary.btn--active {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.btn--ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-primary);
}

.btn--ghost:hover:not(:disabled) {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-border-secondary);
}

.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn--outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.btn--danger {
  background: #ff3b30;
  color: var(--color-text-primary);
}

.btn--danger:hover:not(:disabled) {
  background: #d70015;
}

.btn--success {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.btn--success:hover:not(:disabled) {
  background: var(--color-primary-hover);
}

/* Button Sizes */
.btn--xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  min-height: 32px;
  border-radius: var(--radius-lg);
}

.btn--sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
  border-radius: var(--radius-lg);
}

.btn--md {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-height: 44px;
  border-radius: var(--radius-xl);
}

.btn--lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
  border-radius: var(--radius-xl);
}

.btn--xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-xl);
  min-height: 60px;
  border-radius: var(--radius-2xl);
}

/* Button States */
.btn--loading {
  pointer-events: none;
}

.btn--loading .btn__text {
  opacity: 0;
}

.btn--loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: btnSpin 1s linear infinite;
}

@keyframes btnSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Icon buttons */
.btn--icon-only {
  padding: var(--spacing-sm);
  min-width: 44px;
  border-radius: var(--radius-full);
}

.btn--icon-only.btn--xs {
  padding: var(--spacing-xs);
  min-width: 32px;
}

.btn--icon-only.btn--sm {
  padding: var(--spacing-xs);
  min-width: 36px;
}

.btn--icon-only.btn--lg {
  padding: var(--spacing-md);
  min-width: 52px;
}

.btn--icon-only.btn--xl {
  padding: var(--spacing-lg);
  min-width: 60px;
}

/* Button with icon */
.btn__icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.btn--sm .btn__icon {
  width: 14px;
  height: 14px;
}

.btn--lg .btn__icon {
  width: 18px;
  height: 18px;
}

.btn--xl .btn__icon {
  width: 20px;
  height: 20px;
}

/* Full width button */
.btn--full {
  width: 100%;
}

/* Button group */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right: 1px solid var(--color-border-primary);
}

.btn-group .btn:first-child {
  border-radius: var(--radius-xl) 0 0 var(--radius-xl);
}

.btn-group .btn:last-child {
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  border-right: none;
}

.btn-group .btn:only-child {
  border-radius: var(--radius-xl);
  border-right: none;
}

/* Floating action button */
.btn--fab {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
}

.btn--fab:hover {
  box-shadow: var(--shadow-xl);
  transform: scale(1.05);
}

/* Ripple effect */
.btn--ripple {
  position: relative;
  overflow: hidden;
}

.btn--ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn--ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
  
  .btn--loading::after {
    animation: none;
  }
  
  .btn--ripple::before {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
  
  .btn--ghost,
  .btn--outline {
    border-width: 2px;
  }
}
