/**
 * Button Component
 * 按钮组件 - 支持多种样式、尺寸和状态
 */
class Button {
  constructor(options = {}) {
    this.options = {
      text: options.text || 'Button',
      variant: options.variant || 'primary', // primary, secondary, ghost, outline, danger, success
      size: options.size || 'md', // xs, sm, md, lg, xl
      disabled: options.disabled || false,
      loading: options.loading || false,
      icon: options.icon || null,
      iconPosition: options.iconPosition || 'left', // left, right
      iconOnly: options.iconOnly || false,
      fullWidth: options.fullWidth || false,
      ripple: options.ripple !== false,
      onClick: options.onClick || null,
      type: options.type || 'button', // button, submit, reset
      ariaLabel: options.ariaLabel || null,
      ...options
    };
    
    this.element = null;
    this.isPressed = false;
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('button');
    this.element.type = this.options.type;
    this.updateClasses();
    this.updateAttributes();
  }
  
  updateClasses() {
    const { variant, size, disabled, loading, iconOnly, fullWidth, ripple } = this.options;
    
    const classes = [
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      disabled && 'btn--disabled',
      loading && 'btn--loading',
      iconOnly && 'btn--icon-only',
      fullWidth && 'btn--full',
      ripple && 'btn--ripple'
    ].filter(Boolean);
    
    this.element.className = classes.join(' ');
  }
  
  updateAttributes() {
    const { disabled, loading, ariaLabel, text } = this.options;
    
    this.element.disabled = disabled || loading;
    
    if (ariaLabel) {
      this.element.setAttribute('aria-label', ariaLabel);
    } else if (this.options.iconOnly) {
      this.element.setAttribute('aria-label', text);
    }
    
    if (loading) {
      this.element.setAttribute('aria-busy', 'true');
    } else {
      this.element.removeAttribute('aria-busy');
    }
  }
  
  render() {
    const { text, icon, iconPosition, iconOnly, loading } = this.options;
    
    let content = '';
    
    if (iconOnly) {
      content = this.renderIcon(icon);
    } else {
      const textElement = `<span class="btn__text">${text}</span>`;
      const iconElement = icon ? this.renderIcon(icon) : '';
      
      if (iconPosition === 'right') {
        content = textElement + iconElement;
      } else {
        content = iconElement + textElement;
      }
    }
    
    this.element.innerHTML = content;
  }
  
  renderIcon(icon) {
    if (!icon) return '';
    
    if (typeof icon === 'string') {
      // Assume it's an emoji or text icon
      return `<span class="btn__icon">${icon}</span>`;
    } else if (icon.svg) {
      // SVG icon
      return `<span class="btn__icon">${icon.svg}</span>`;
    } else if (icon.className) {
      // CSS class icon (like Font Awesome)
      return `<span class="btn__icon ${icon.className}"></span>`;
    }
    
    return '';
  }
  
  // Public methods
  setText(text) {
    this.options.text = text;
    this.render();
    this.updateAttributes();
  }
  
  setVariant(variant) {
    this.options.variant = variant;
    this.updateClasses();
  }
  
  setSize(size) {
    this.options.size = size;
    this.updateClasses();
  }
  
  setDisabled(disabled) {
    this.options.disabled = disabled;
    this.updateClasses();
    this.updateAttributes();
  }
  
  setLoading(loading) {
    this.options.loading = loading;
    this.updateClasses();
    this.updateAttributes();
  }
  
  setIcon(icon, position = 'left') {
    this.options.icon = icon;
    this.options.iconPosition = position;
    this.render();
  }
  
  toggle() {
    this.element.classList.toggle('btn--active');
    return this.element.classList.contains('btn--active');
  }
  
  isActive() {
    return this.element.classList.contains('btn--active');
  }
  
  setActive(active) {
    if (active) {
      this.element.classList.add('btn--active');
    } else {
      this.element.classList.remove('btn--active');
    }
  }
  
  // Event handling
  bindEvents() {
    this.element.addEventListener('click', this.handleClick.bind(this));
    this.element.addEventListener('keydown', this.handleKeydown.bind(this));
    
    if (this.options.ripple) {
      this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));
      this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));
      this.element.addEventListener('mouseleave', this.handleMouseUp.bind(this));
    }
  }
  
  handleClick(event) {
    if (this.options.disabled || this.options.loading) {
      event.preventDefault();
      return;
    }
    
    if (this.options.onClick) {
      this.options.onClick(event, this);
    }
  }
  
  handleKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
      if (!this.options.disabled && !this.options.loading) {
        this.handleClick(event);
      }
    }
  }
  
  handleMouseDown(event) {
    this.isPressed = true;
    this.element.classList.add('btn--pressed');
  }
  
  handleMouseUp(event) {
    this.isPressed = false;
    this.element.classList.remove('btn--pressed');
  }
  
  // Lifecycle methods
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
  
  destroy() {
    this.unmount();
    this.element = null;
    this.options = null;
  }
  
  // Static factory methods
  static create(options) {
    return new Button(options);
  }
  
  static createPrimary(text, onClick) {
    return new Button({ text, variant: 'primary', onClick });
  }
  
  static createSecondary(text, onClick) {
    return new Button({ text, variant: 'secondary', onClick });
  }
  
  static createGhost(text, onClick) {
    return new Button({ text, variant: 'ghost', onClick });
  }
  
  static createIconButton(icon, onClick, ariaLabel) {
    return new Button({ 
      icon, 
      iconOnly: true, 
      variant: 'ghost', 
      onClick, 
      ariaLabel 
    });
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Button;
}

// Global registration
if (typeof window !== 'undefined') {
  window.Button = Button;
}
