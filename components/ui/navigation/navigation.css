/* Navigation Component Styles */

/* Tab Navigation */
.nav-tabs {
  display: flex;
  padding: 0 var(--spacing-xl);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-xl);
  background: var(--color-background-tertiary);
  color: var(--color-text-secondary);
  border: none;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
  white-space: nowrap;
  flex-shrink: 0;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-tab:hover {
  background: var(--color-background-quaternary);
  color: var(--color-text-primary);
}

.nav-tab:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.nav-tab.nav-tab--active {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.nav-tab.nav-tab--active:hover {
  background: var(--color-primary-hover);
}

/* Tab sizes */
.nav-tab--sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 32px;
  border-radius: var(--radius-lg);
}

.nav-tab--lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 44px;
  border-radius: var(--radius-2xl);
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--color-background-primary);
  padding: var(--spacing-sm) 0 var(--spacing-3xl);
  border-top: 1px solid var(--color-border-primary);
  z-index: var(--z-fixed);
}

.bottom-nav--relative {
  position: relative;
  border-top: 1px solid var(--color-border-primary);
}

.nav-items {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  min-width: 44px;
  text-align: center;
}

.nav-item:hover {
  color: var(--color-text-primary);
  background: var(--color-background-secondary);
}

.nav-item:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.nav-item.nav-item--active {
  color: var(--color-text-primary);
}

.nav-item.nav-item--active .nav-icon {
  background: var(--color-primary);
}

.nav-icon {
  width: 24px;
  height: 24px;
  background: currentColor;
  border-radius: var(--radius-sm);
  transition: var(--transition-all);
  flex-shrink: 0;
  position: relative;
}

.nav-icon--home::before {
  content: '🏠';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
}

.nav-icon--search::before {
  content: '🔍';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
}

.nav-icon--library::before {
  content: '📚';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
}

.nav-icon--create::before {
  content: '➕';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
}

/* Badge for nav items */
.nav-item__badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ff3b30;
  color: white;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.nav-item__badge--dot {
  width: 8px;
  height: 8px;
  padding: 0;
  min-width: 0;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.breadcrumb::-webkit-scrollbar {
  display: none;
}

.breadcrumb__item {
  white-space: nowrap;
  flex-shrink: 0;
}

.breadcrumb__link {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
}

.breadcrumb__link:hover {
  color: var(--color-text-primary);
}

.breadcrumb__link--active {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.breadcrumb__separator {
  color: var(--color-text-tertiary);
  margin: 0 var(--spacing-xs);
}

/* Sidebar Navigation */
.sidebar-nav {
  width: 280px;
  height: 100vh;
  background: var(--color-surface-primary);
  border-right: 1px solid var(--color-border-primary);
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.sidebar-nav__header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border-secondary);
}

.sidebar-nav__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.sidebar-nav__section {
  margin-bottom: var(--spacing-xl);
}

.sidebar-nav__section-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-md);
}

.sidebar-nav__item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: var(--transition-all);
  margin-bottom: var(--spacing-xs);
}

.sidebar-nav__item:hover {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.sidebar-nav__item--active {
  background: var(--color-primary);
  color: var(--color-text-primary);
}

.sidebar-nav__icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .sidebar-nav {
    position: fixed;
    top: 0;
    left: -280px;
    z-index: var(--z-modal);
    transition: left var(--transition-normal);
  }
  
  .sidebar-nav--open {
    left: 0;
  }
  
  .nav-tabs {
    padding: 0 var(--spacing-lg);
  }
  
  .nav-tab {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}

/* Home indicator for iOS */
.home-indicator {
  position: absolute;
  bottom: var(--spacing-sm);
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: var(--color-text-primary);
  border-radius: var(--radius-sm);
  opacity: 0.3;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .nav-tab,
  .nav-item,
  .breadcrumb__link,
  .sidebar-nav__item {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .nav-tab,
  .nav-item {
    border: 1px solid currentColor;
  }
  
  .bottom-nav {
    border-top: 2px solid var(--color-border-primary);
  }
}
