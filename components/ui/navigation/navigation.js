/**
 * Navigation Components
 * 导航组件集合 - 包含标签导航、底部导航、面包屑等
 */

// Tab Navigation Component
class TabNavigation {
  constructor(options = {}) {
    this.options = {
      tabs: options.tabs || [],
      activeTab: options.activeTab || 0,
      size: options.size || 'md', // sm, md, lg
      onChange: options.onChange || null,
      ...options
    };
    
    this.element = null;
    this.activeIndex = this.options.activeTab;
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('div');
    this.element.className = 'nav-tabs';
    this.element.setAttribute('role', 'tablist');
  }
  
  render() {
    const { tabs, size } = this.options;
    
    this.element.innerHTML = tabs.map((tab, index) => {
      const isActive = index === this.activeIndex;
      const sizeClass = size !== 'md' ? `nav-tab--${size}` : '';
      const activeClass = isActive ? 'nav-tab--active' : '';
      
      return `
        <button class="nav-tab ${sizeClass} ${activeClass}" 
                role="tab"
                aria-selected="${isActive}"
                aria-controls="tabpanel-${index}"
                data-index="${index}">
          ${tab.label || tab}
        </button>
      `;
    }).join('');
  }
  
  bindEvents() {
    this.element.addEventListener('click', this.handleTabClick.bind(this));
    this.element.addEventListener('keydown', this.handleKeydown.bind(this));
  }
  
  handleTabClick(event) {
    const button = event.target.closest('.nav-tab');
    if (!button) return;
    
    const index = parseInt(button.dataset.index);
    this.setActiveTab(index);
  }
  
  handleKeydown(event) {
    const currentTab = event.target.closest('.nav-tab');
    if (!currentTab) return;
    
    const tabs = Array.from(this.element.querySelectorAll('.nav-tab'));
    const currentIndex = tabs.indexOf(currentTab);
    
    let newIndex = currentIndex;
    
    switch (event.key) {
      case 'ArrowLeft':
        newIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
        break;
      case 'ArrowRight':
        newIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = tabs.length - 1;
        break;
      default:
        return;
    }
    
    event.preventDefault();
    tabs[newIndex].focus();
    this.setActiveTab(newIndex);
  }
  
  setActiveTab(index) {
    if (index < 0 || index >= this.options.tabs.length) return;
    
    this.activeIndex = index;
    this.render();
    
    if (this.options.onChange) {
      this.options.onChange(index, this.options.tabs[index]);
    }
  }
  
  addTab(tab, index = -1) {
    if (index === -1) {
      this.options.tabs.push(tab);
    } else {
      this.options.tabs.splice(index, 0, tab);
    }
    this.render();
  }
  
  removeTab(index) {
    if (index < 0 || index >= this.options.tabs.length) return;
    
    this.options.tabs.splice(index, 1);
    
    if (this.activeIndex >= this.options.tabs.length) {
      this.activeIndex = Math.max(0, this.options.tabs.length - 1);
    }
    
    this.render();
  }
  
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
  }
}

// Bottom Navigation Component
class BottomNavigation {
  constructor(options = {}) {
    this.options = {
      items: options.items || [],
      activeItem: options.activeItem || 0,
      showHomeIndicator: options.showHomeIndicator !== false,
      position: options.position || 'fixed', // fixed, relative
      onChange: options.onChange || null,
      ...options
    };
    
    this.element = null;
    this.activeIndex = this.options.activeItem;
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('div');
    const positionClass = this.options.position === 'relative' ? 'bottom-nav--relative' : '';
    this.element.className = `bottom-nav ${positionClass}`;
    this.element.setAttribute('role', 'navigation');
    this.element.setAttribute('aria-label', 'Main navigation');
  }
  
  render() {
    const { items, showHomeIndicator } = this.options;
    
    const navItems = items.map((item, index) => {
      const isActive = index === this.activeIndex;
      const activeClass = isActive ? 'nav-item--active' : '';
      const badge = item.badge ? this.renderBadge(item.badge) : '';
      
      return `
        <div class="nav-item ${activeClass}" 
             role="button"
             tabindex="0"
             aria-label="${item.label}"
             data-index="${index}">
          <div class="nav-icon nav-icon--${item.icon}">
            ${badge}
          </div>
          <span>${item.label}</span>
        </div>
      `;
    }).join('');
    
    const homeIndicator = showHomeIndicator ? '<div class="home-indicator"></div>' : '';
    
    this.element.innerHTML = `
      <div class="nav-items">
        ${navItems}
      </div>
      ${homeIndicator}
    `;
  }
  
  renderBadge(badge) {
    if (typeof badge === 'number') {
      return `<div class="nav-item__badge">${badge}</div>`;
    } else if (badge === true) {
      return '<div class="nav-item__badge nav-item__badge--dot"></div>';
    } else if (typeof badge === 'string') {
      return `<div class="nav-item__badge">${badge}</div>`;
    }
    return '';
  }
  
  bindEvents() {
    this.element.addEventListener('click', this.handleItemClick.bind(this));
    this.element.addEventListener('keydown', this.handleKeydown.bind(this));
  }
  
  handleItemClick(event) {
    const item = event.target.closest('.nav-item');
    if (!item) return;
    
    const index = parseInt(item.dataset.index);
    this.setActiveItem(index);
  }
  
  handleKeydown(event) {
    const currentItem = event.target.closest('.nav-item');
    if (!currentItem) return;
    
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.handleItemClick(event);
    }
  }
  
  setActiveItem(index) {
    if (index < 0 || index >= this.options.items.length) return;
    
    this.activeIndex = index;
    this.render();
    
    if (this.options.onChange) {
      this.options.onChange(index, this.options.items[index]);
    }
  }
  
  updateBadge(index, badge) {
    if (index < 0 || index >= this.options.items.length) return;
    
    this.options.items[index].badge = badge;
    this.render();
  }
  
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
  }
}

// Breadcrumb Navigation Component
class Breadcrumb {
  constructor(options = {}) {
    this.options = {
      items: options.items || [],
      separator: options.separator || '/',
      maxItems: options.maxItems || null,
      onClick: options.onClick || null,
      ...options
    };
    
    this.element = null;
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    this.bindEvents();
  }
  
  createElement() {
    this.element = document.createElement('nav');
    this.element.className = 'breadcrumb';
    this.element.setAttribute('aria-label', 'Breadcrumb');
  }
  
  render() {
    const { items, separator, maxItems } = this.options;
    let displayItems = [...items];
    
    if (maxItems && items.length > maxItems) {
      displayItems = [
        items[0],
        { label: '...', href: null, disabled: true },
        ...items.slice(-(maxItems - 2))
      ];
    }
    
    this.element.innerHTML = displayItems.map((item, index) => {
      const isLast = index === displayItems.length - 1;
      const isDisabled = item.disabled;
      
      let content;
      if (isDisabled) {
        content = `<span class="breadcrumb__item">${item.label}</span>`;
      } else if (item.href && !isLast) {
        content = `
          <span class="breadcrumb__item">
            <a href="${item.href}" class="breadcrumb__link" data-index="${index}">
              ${item.label}
            </a>
          </span>
        `;
      } else {
        content = `
          <span class="breadcrumb__item">
            <span class="breadcrumb__link breadcrumb__link--active">
              ${item.label}
            </span>
          </span>
        `;
      }
      
      const separatorElement = !isLast ? 
        `<span class="breadcrumb__separator" aria-hidden="true">${separator}</span>` : '';
      
      return content + separatorElement;
    }).join('');
  }
  
  bindEvents() {
    if (this.options.onClick) {
      this.element.addEventListener('click', this.handleClick.bind(this));
    }
  }
  
  handleClick(event) {
    const link = event.target.closest('.breadcrumb__link');
    if (!link || link.classList.contains('breadcrumb__link--active')) return;
    
    const index = parseInt(link.dataset.index);
    if (isNaN(index)) return;
    
    event.preventDefault();
    
    if (this.options.onClick) {
      this.options.onClick(index, this.options.items[index]);
    }
  }
  
  setItems(items) {
    this.options.items = items;
    this.render();
  }
  
  addItem(item) {
    this.options.items.push(item);
    this.render();
  }
  
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { TabNavigation, BottomNavigation, Breadcrumb };
}

// Global registration
if (typeof window !== 'undefined') {
  window.TabNavigation = TabNavigation;
  window.BottomNavigation = BottomNavigation;
  window.Breadcrumb = Breadcrumb;
}
