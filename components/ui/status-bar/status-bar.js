/**
 * StatusBar Component
 * 状态栏组件 - 显示时间、信号、WiFi和电池状态
 */
class StatusBar {
  constructor(options = {}) {
    this.options = {
      time: options.time || this.getCurrentTime(),
      signalStrength: options.signalStrength || 'strong', // weak, medium, strong
      wifiConnected: options.wifiConnected !== false,
      batteryLevel: options.batteryLevel || 80,
      isCharging: options.isCharging || false,
      autoUpdate: options.autoUpdate !== false,
      ...options
    };
    
    this.element = null;
    this.updateInterval = null;
    
    this.init();
  }
  
  init() {
    this.createElement();
    this.render();
    
    if (this.options.autoUpdate) {
      this.startAutoUpdate();
    }
  }
  
  createElement() {
    this.element = document.createElement('div');
    this.element.className = 'status-bar';
    this.element.setAttribute('role', 'banner');
    this.element.setAttribute('aria-label', 'Status bar');
  }
  
  getCurrentTime() {
    const now = new Date();
    return now.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: false 
    });
  }
  
  render() {
    const { time, signalStrength, wifiConnected, batteryLevel, isCharging } = this.options;
    
    this.element.innerHTML = `
      <div class="status-bar__left">
        <span class="status-bar__time">${time}</span>
      </div>
      <div class="status-bar__right">
        ${this.renderSignalBars(signalStrength)}
        ${this.renderWifiIcon(wifiConnected)}
        ${this.renderBattery(batteryLevel, isCharging)}
      </div>
    `;
  }
  
  renderSignalBars(strength) {
    const strengthClass = strength === 'weak' ? 'signal-bars__bar--weak' :
                         strength === 'medium' ? 'signal-bars__bar--medium' :
                         'signal-bars__bar--strong';
    
    return `
      <div class="signal-bars" aria-label="Signal strength: ${strength}">
        <div class="signal-bars__bar ${strengthClass}"></div>
        <div class="signal-bars__bar ${strengthClass}"></div>
        <div class="signal-bars__bar ${strengthClass}"></div>
        <div class="signal-bars__bar ${strengthClass}"></div>
      </div>
    `;
  }
  
  renderWifiIcon(connected) {
    const connectionClass = connected ? '' : 'wifi-icon--disconnected';
    const ariaLabel = connected ? 'WiFi connected' : 'WiFi disconnected';
    
    return `
      <div class="wifi-icon ${connectionClass}" 
           aria-label="${ariaLabel}"
           role="img">
      </div>
    `;
  }
  
  renderBattery(level, charging) {
    const chargingClass = charging ? 'battery--charging' : '';
    const levelClass = this.getBatteryLevelClass(level);
    const fillClass = this.getBatteryFillClass(level);
    const ariaLabel = `Battery ${level}%${charging ? ', charging' : ''}`;
    
    return `
      <div class="battery ${chargingClass}" 
           aria-label="${ariaLabel}"
           role="img">
        <div class="battery__fill ${levelClass} ${fillClass}"></div>
      </div>
    `;
  }
  
  getBatteryLevelClass(level) {
    if (level <= 20) return 'battery__fill--low';
    if (level <= 50) return 'battery__fill--medium';
    if (level <= 80) return 'battery__fill--high';
    return 'battery__fill--full';
  }
  
  getBatteryFillClass(level) {
    const roundedLevel = Math.round(level / 10) * 10;
    return `battery__fill--${Math.max(10, Math.min(100, roundedLevel))}`;
  }
  
  // Public methods
  updateTime(time) {
    this.options.time = time || this.getCurrentTime();
    this.render();
  }
  
  updateSignalStrength(strength) {
    this.options.signalStrength = strength;
    this.render();
  }
  
  updateWifiStatus(connected) {
    this.options.wifiConnected = connected;
    this.render();
  }
  
  updateBattery(level, charging) {
    this.options.batteryLevel = level;
    if (charging !== undefined) {
      this.options.isCharging = charging;
    }
    this.render();
  }
  
  startAutoUpdate() {
    this.updateInterval = setInterval(() => {
      this.updateTime();
    }, 1000);
  }
  
  stopAutoUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
  
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }
  
  unmount() {
    this.stopAutoUpdate();
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
  
  destroy() {
    this.unmount();
    this.element = null;
    this.options = null;
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StatusBar;
}

// Global registration
if (typeof window !== 'undefined') {
  window.StatusBar = StatusBar;
}
