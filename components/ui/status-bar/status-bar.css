/* StatusBar Component Styles */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  background: var(--color-background-primary);
  position: relative;
  z-index: var(--z-sticky);
}

.status-bar__left {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
}

.status-bar__right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* Signal Bars */
.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.signal-bars__bar {
  width: 3px;
  background: var(--color-text-primary);
  border-radius: var(--radius-xs);
  transition: var(--transition-fast);
}

.signal-bars__bar:nth-child(1) { height: 4px; }
.signal-bars__bar:nth-child(2) { height: 6px; }
.signal-bars__bar:nth-child(3) { height: 8px; }
.signal-bars__bar:nth-child(4) { height: 10px; }

.signal-bars__bar--weak:nth-child(n+2) {
  background: var(--color-text-tertiary);
}

.signal-bars__bar--medium:nth-child(n+3) {
  background: var(--color-text-tertiary);
}

.signal-bars__bar--strong {
  background: var(--color-text-primary);
}

/* WiFi Icon */
.wifi-icon {
  width: 15px;
  height: 15px;
  background: var(--color-text-primary);
  border-radius: var(--radius-full);
  position: relative;
  transition: var(--transition-fast);
}

.wifi-icon--disconnected {
  background: var(--color-text-tertiary);
}

.wifi-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--color-background-primary);
  border-radius: var(--radius-full);
}

/* Battery */
.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--color-text-primary);
  border-radius: var(--radius-xs);
  position: relative;
  transition: var(--transition-fast);
}

.battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--color-text-primary);
  border-radius: 0 var(--radius-xs) var(--radius-xs) 0;
}

.battery__fill {
  height: 100%;
  background: var(--color-text-primary);
  border-radius: var(--radius-xs);
  transition: var(--transition-normal);
}

.battery__fill--low {
  background: #ff3b30;
}

.battery__fill--medium {
  background: #ff9500;
}

.battery__fill--high {
  background: var(--color-primary);
}

.battery__fill--full {
  background: var(--color-primary);
}

/* Battery levels */
.battery__fill--10 { width: 10%; }
.battery__fill--20 { width: 20%; }
.battery__fill--30 { width: 30%; }
.battery__fill--40 { width: 40%; }
.battery__fill--50 { width: 50%; }
.battery__fill--60 { width: 60%; }
.battery__fill--70 { width: 70%; }
.battery__fill--80 { width: 80%; }
.battery__fill--90 { width: 90%; }
.battery__fill--100 { width: 100%; }

/* Charging state */
.battery--charging .battery__fill {
  animation: batteryCharging 2s ease-in-out infinite;
}

@keyframes batteryCharging {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .status-bar {
    background: var(--color-background-primary);
  }
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .status-bar {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  }
  
  .status-bar__left {
    font-size: var(--font-size-lg);
  }
  
  .status-bar__right {
    gap: var(--spacing-xs);
  }
}
