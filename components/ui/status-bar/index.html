<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StatusBar Component</title>
    <link rel="stylesheet" href="../../../design-tokens.css">
    <link rel="stylesheet" href="status-bar.css">
</head>
<body>
    <div class="component-demo">
        <h1>StatusBar Component</h1>
        
        <div class="demo-section">
            <h2>Default StatusBar</h2>
            <div id="status-bar-default"></div>
        </div>
        
        <div class="demo-section">
            <h2>Low Battery</h2>
            <div id="status-bar-low-battery"></div>
        </div>
        
        <div class="demo-section">
            <h2>Charging</h2>
            <div id="status-bar-charging"></div>
        </div>
        
        <div class="demo-section">
            <h2>Weak Signal</h2>
            <div id="status-bar-weak-signal"></div>
        </div>
        
        <div class="demo-section">
            <h2>No WiFi</h2>
            <div id="status-bar-no-wifi"></div>
        </div>
        
        <div class="demo-controls">
            <h3>Interactive Controls</h3>
            <div class="control-group">
                <label for="battery-level">Battery Level:</label>
                <input type="range" id="battery-level" min="0" max="100" value="80">
                <span id="battery-value">80%</span>
            </div>
            
            <div class="control-group">
                <label for="signal-strength">Signal Strength:</label>
                <select id="signal-strength">
                    <option value="weak">Weak</option>
                    <option value="medium">Medium</option>
                    <option value="strong" selected>Strong</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="wifi-connected" checked>
                    WiFi Connected
                </label>
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="is-charging">
                    Charging
                </label>
            </div>
        </div>
    </div>

    <script src="status-bar.js"></script>
    <script>
        // Demo instances
        const defaultStatusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 80,
            signalStrength: 'strong',
            wifiConnected: true,
            autoUpdate: false
        }).mount('#status-bar-default');

        const lowBatteryStatusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 15,
            signalStrength: 'strong',
            wifiConnected: true,
            autoUpdate: false
        }).mount('#status-bar-low-battery');

        const chargingStatusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 45,
            signalStrength: 'medium',
            wifiConnected: true,
            isCharging: true,
            autoUpdate: false
        }).mount('#status-bar-charging');

        const weakSignalStatusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 60,
            signalStrength: 'weak',
            wifiConnected: true,
            autoUpdate: false
        }).mount('#status-bar-weak-signal');

        const noWifiStatusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 90,
            signalStrength: 'strong',
            wifiConnected: false,
            autoUpdate: false
        }).mount('#status-bar-no-wifi');

        // Interactive controls
        const batteryLevelSlider = document.getElementById('battery-level');
        const batteryValueSpan = document.getElementById('battery-value');
        const signalStrengthSelect = document.getElementById('signal-strength');
        const wifiConnectedCheckbox = document.getElementById('wifi-connected');
        const isChargingCheckbox = document.getElementById('is-charging');

        batteryLevelSlider.addEventListener('input', (e) => {
            const level = parseInt(e.target.value);
            batteryValueSpan.textContent = `${level}%`;
            defaultStatusBar.updateBattery(level, isChargingCheckbox.checked);
        });

        signalStrengthSelect.addEventListener('change', (e) => {
            defaultStatusBar.updateSignalStrength(e.target.value);
        });

        wifiConnectedCheckbox.addEventListener('change', (e) => {
            defaultStatusBar.updateWifiStatus(e.target.checked);
        });

        isChargingCheckbox.addEventListener('change', (e) => {
            defaultStatusBar.updateBattery(
                parseInt(batteryLevelSlider.value), 
                e.target.checked
            );
        });
    </script>

    <style>
        .component-demo {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            background: var(--color-background-secondary);
            min-height: 100vh;
        }

        .component-demo h1 {
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-3xl);
            text-align: center;
        }

        .demo-section {
            margin-bottom: var(--spacing-3xl);
            padding: var(--spacing-xl);
            background: var(--color-surface-primary);
            border-radius: var(--radius-lg);
        }

        .demo-section h2 {
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
            font-size: var(--font-size-lg);
        }

        .demo-controls {
            background: var(--color-surface-primary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
        }

        .demo-controls h3 {
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .control-group {
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .control-group label {
            color: var(--color-text-secondary);
            min-width: 120px;
        }

        .control-group input[type="range"] {
            flex: 1;
            max-width: 200px;
        }

        .control-group select {
            background: var(--color-background-secondary);
            color: var(--color-text-primary);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        #battery-value {
            color: var(--color-text-primary);
            font-weight: var(--font-weight-medium);
            min-width: 40px;
        }
    </style>
</body>
</html>
