<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design System - Usage Example</title>
    <link rel="stylesheet" href="design-tokens.css">
    <link rel="stylesheet" href="components/ui/status-bar/status-bar.css">
    <link rel="stylesheet" href="components/ui/button/button.css">
    <link rel="stylesheet" href="components/ui/card/card.css">
    <link rel="stylesheet" href="components/ui/navigation/navigation.css">
    <link rel="stylesheet" href="components/ui/grid/grid.css">
    <style>
        body {
            margin: 0;
            padding: var(--spacing-xl);
            background: var(--color-background-primary);
            min-height: 100vh;
        }

        .example-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .example-header {
            text-align: center;
            margin-bottom: var(--spacing-4xl);
        }

        .example-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-md);
        }

        .example-subtitle {
            font-size: var(--font-size-lg);
            color: var(--color-text-secondary);
        }

        .example-section {
            margin-bottom: var(--spacing-4xl);
            background: var(--color-surface-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
        }

        .section-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .code-block {
            background: var(--color-background-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            font-family: var(--font-family-mono);
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            overflow-x: auto;
        }

        .demo-area {
            background: var(--color-background-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin: var(--spacing-lg) 0;
        }

        .phone-demo {
            width: 375px;
            height: 600px;
            background: var(--color-background-primary);
            border-radius: var(--radius-2xl);
            border: 3px solid var(--color-border-primary);
            margin: 0 auto;
            overflow: hidden;
            position: relative;
        }

        .quick-actions {
            display: flex;
            gap: var(--spacing-md);
            margin: var(--spacing-lg) 0;
            flex-wrap: wrap;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
        }

        .stat-card {
            background: var(--color-background-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
        }

        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
        }

        @media (max-width: 768px) {
            .phone-demo {
                width: 100%;
                max-width: 375px;
            }
            
            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="example-container">
        <!-- Header -->
        <header class="example-header">
            <h1 class="example-title">Design System Usage</h1>
            <p class="example-subtitle">
                Learn how to use our design system components to build beautiful interfaces
            </p>
        </header>

        <!-- Quick Start Section -->
        <section class="example-section">
            <h2 class="section-title">🚀 Quick Start</h2>
            <p style="color: var(--color-text-secondary); margin-bottom: var(--spacing-lg);">
                Get started with our design system in just a few steps:
            </p>

            <div class="code-block">
// 1. Include the design tokens and component styles
&lt;link rel="stylesheet" href="design-tokens.css"&gt;
&lt;link rel="stylesheet" href="components/ui/button/button.css"&gt;

// 2. Include the component scripts
&lt;script src="components/ui/button/button.js"&gt;&lt;/script&gt;
&lt;script src="components/index.js"&gt;&lt;/script&gt;

// 3. Create components using the design system
const button = DS.createButton({
    text: 'Hello World',
    variant: 'primary',
    onClick: () => alert('Button clicked!')
}).mount('#my-container');
            </div>

            <div class="quick-actions">
                <div id="quick-button-1"></div>
                <div id="quick-button-2"></div>
                <div id="quick-button-3"></div>
            </div>
        </section>

        <!-- Component Examples Section -->
        <section class="example-section">
            <h2 class="section-title">🧩 Component Examples</h2>
            
            <h3 style="color: var(--color-text-primary); margin-bottom: var(--spacing-md);">Status Bar</h3>
            <div class="demo-area">
                <div id="status-bar-example"></div>
            </div>
            <div class="code-block">
const statusBar = DS.createStatusBar({
    time: '9:41',
    batteryLevel: 75,
    signalStrength: 'strong',
    wifiConnected: true
}).mount('#status-bar-container');
            </div>

            <h3 style="color: var(--color-text-primary); margin: var(--spacing-xl) 0 var(--spacing-md);">Cards</h3>
            <div class="demo-area">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
                    <div id="card-example-1"></div>
                    <div id="card-example-2"></div>
                    <div id="card-example-3"></div>
                </div>
            </div>
            <div class="code-block">
// Music Card
const musicCard = DS.createMusicCard({
    icon: '🎵',
    title: 'My Playlist',
    backgroundColor: '#4CAF50'
}).mount('#container');

// Featured Card
const featuredCard = DS.createFeaturedCard({
    avatar: '🎙️',
    badge: 'Podcast',
    title: 'Featured Content',
    description: 'Description here...'
}).mount('#container');
            </div>

            <h3 style="color: var(--color-text-primary); margin: var(--spacing-xl) 0 var(--spacing-md);">Navigation</h3>
            <div class="demo-area">
                <div id="navigation-example"></div>
            </div>
            <div class="code-block">
const tabNav = DS.createTabNavigation({
    tabs: ['Home', 'Music', 'Podcasts', 'Library'],
    activeTab: 0,
    onChange: (index, tab) => console.log('Tab changed:', tab)
}).mount('#nav-container');
            </div>
        </section>

        <!-- Complete App Example -->
        <section class="example-section">
            <h2 class="section-title">📱 Complete App Example</h2>
            <p style="color: var(--color-text-secondary); margin-bottom: var(--spacing-lg);">
                See how all components work together in a complete music app interface:
            </p>

            <div class="demo-area">
                <div class="phone-demo">
                    <div id="complete-app-demo"></div>
                </div>
            </div>

            <div class="code-block">
// Build a complete music app with one method call
const app = DS.buildMusicApp('#app-container', {
    data: {
        musicItems: [
            { title: 'Song 1', icon: '🎵', color: '#4CAF50' },
            { title: 'Song 2', icon: '🎭', color: '#FF5722' }
        ]
    }
});

// Access individual components
app.statusBar.updateBattery(50, true);
app.musicGrid.addItem({ title: 'New Song', icon: '🎶', color: '#9C27B0' });
            </div>

            <div class="quick-actions">
                <div id="app-action-1"></div>
                <div id="app-action-2"></div>
                <div id="app-action-3"></div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="example-section">
            <h2 class="section-title">📊 Design System Stats</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Core Components</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Design Tokens</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">DRY Compliance</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">A11Y</div>
                    <div class="stat-label">Accessible</div>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="components/ui/status-bar/status-bar.js"></script>
    <script src="components/ui/button/button.js"></script>
    <script src="components/ui/card/card.js"></script>
    <script src="components/ui/navigation/navigation.js"></script>
    <script src="components/ui/grid/grid.js"></script>
    <script src="components/index.js"></script>

    <script>
        // Initialize examples
        document.addEventListener('DOMContentLoaded', () => {
            // Quick start buttons
            DS.createButton({
                text: 'Primary',
                variant: 'primary',
                onClick: () => alert('Primary button clicked!')
            }).mount('#quick-button-1');

            DS.createButton({
                text: 'Secondary',
                variant: 'secondary',
                onClick: () => alert('Secondary button clicked!')
            }).mount('#quick-button-2');

            DS.createButton({
                text: 'With Icon',
                variant: 'outline',
                icon: '🎵',
                onClick: () => alert('Icon button clicked!')
            }).mount('#quick-button-3');

            // Status bar example
            DS.createStatusBar({
                time: '9:41',
                batteryLevel: 75,
                signalStrength: 'strong',
                wifiConnected: true,
                autoUpdate: false
            }).mount('#status-bar-example');

            // Card examples
            DS.createMusicCard({
                icon: '🎵',
                title: 'My Playlist',
                backgroundColor: '#4CAF50'
            }).mount('#card-example-1');

            DS.createMusicCard({
                icon: '🎭',
                title: 'Broadway Hits',
                backgroundColor: '#FF5722'
            }).mount('#card-example-2');

            DS.createFeaturedCard({
                avatar: '🎙️',
                badge: 'Podcast',
                title: 'Featured Show',
                description: 'An amazing podcast about music and culture.'
            }).mount('#card-example-3');

            // Navigation example
            DS.createTabNavigation({
                tabs: ['Home', 'Music', 'Podcasts', 'Library'],
                activeTab: 1,
                onChange: (index, tab) => {
                    console.log('Tab changed:', tab);
                }
            }).mount('#navigation-example');

            // Complete app example
            const completeApp = DS.buildMusicApp('#complete-app-demo');

            // App action buttons
            DS.createButton({
                text: 'Add Song',
                variant: 'primary',
                size: 'sm',
                onClick: () => {
                    completeApp.musicGrid.addItem({
                        title: `Song ${Date.now()}`,
                        icon: '🎶',
                        color: '#' + Math.floor(Math.random()*16777215).toString(16)
                    });
                }
            }).mount('#app-action-1');

            DS.createButton({
                text: 'Low Battery',
                variant: 'secondary',
                size: 'sm',
                onClick: () => {
                    completeApp.statusBar.updateBattery(15, false);
                }
            }).mount('#app-action-2');

            DS.createButton({
                text: 'Shuffle',
                variant: 'ghost',
                size: 'sm',
                onClick: () => {
                    const items = completeApp.musicGrid.getItems();
                    const shuffled = items.sort(() => Math.random() - 0.5);
                    completeApp.musicGrid.setItems(shuffled);
                }
            }).mount('#app-action-3');

            console.log('🎉 All examples initialized successfully!');
        });
    </script>
</body>
</html>
