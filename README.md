# Music App Design System

一个完整的设计系统，从现有音乐应用页面中抽取可复用组件，实现 DRY 原则，并构建统一的设计语言。

## 🎯 项目目标

- **组件化架构**: 将 UI 拆分为可复用的独立组件
- **DRY 原则**: 消除重复代码和样式
- **设计系统**: 统一的设计令牌、组件库和使用规范
- **可维护性**: 模块化结构，易于维护和扩展

## 📁 项目结构

```
├── design-tokens.css           # 设计令牌 - 颜色、字体、间距等
├── components/ui/              # UI 组件库
│   ├── status-bar/            # 状态栏组件
│   │   ├── status-bar.css
│   │   ├── status-bar.js
│   │   └── index.html
│   ├── button/                # 按钮组件
│   │   ├── button.css
│   │   ├── button.js
│   │   └── index.html
│   ├── card/                  # 卡片组件
│   │   ├── card.css
│   │   ├── card.js
│   │   └── index.html
│   ├── navigation/            # 导航组件
│   │   ├── navigation.css
│   │   ├── navigation.js
│   │   └── index.html
│   └── grid/                  # 网格组件
│       ├── grid.css
│       ├── grid.js
│       └── index.html
├── playground/                # 组件预览页面
│   └── index.html
├── music-app.html            # 原始音乐应用
├── music-app-refactored.html # 重构后的音乐应用
└── README.md
```

## 🎨 设计令牌 (Design Tokens)

### 颜色系统
- **Primary**: `#30d158` - 主要操作和强调
- **Secondary**: `#007aff` - 次要操作
- **Background**: `#000000` - 主背景
- **Surface**: `#1c1c1e` - 卡片和组件背景
- **Text Primary**: `#ffffff` - 主要文本
- **Text Secondary**: `#999999` - 次要文本

### 字体系统
- **Font Family**: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`
- **Font Sizes**: `xs(10px)`, `sm(12px)`, `base(14px)`, `lg(16px)`, `xl(17px)`, `2xl(22px)`
- **Font Weights**: `normal(400)`, `medium(500)`, `semibold(600)`, `bold(700)`

### 间距系统
- **Spacing Scale**: `xs(4px)`, `sm(8px)`, `md(12px)`, `lg(15px)`, `xl(20px)`, `2xl(24px)`, `3xl(30px)`, `4xl(40px)`

### 圆角系统
- **Border Radius**: `xs(2px)`, `sm(4px)`, `md(8px)`, `lg(12px)`, `xl(20px)`, `2xl(24px)`, `full(50%)`

## 🧩 组件库

### StatusBar 组件
状态栏组件，显示时间、信号强度、WiFi 和电池状态。

**特性**:
- 自动时间更新
- 可配置的信号强度
- WiFi 连接状态
- 电池电量和充电状态
- 响应式设计

**使用示例**:
```javascript
const statusBar = new StatusBar({
    time: '9:41',
    batteryLevel: 80,
    signalStrength: 'strong',
    wifiConnected: true,
    isCharging: false
}).mount('#status-bar-container');
```

### Button 组件
多功能按钮组件，支持多种样式、尺寸和状态。

**变体**: `primary`, `secondary`, `ghost`, `outline`, `danger`, `success`
**尺寸**: `xs`, `sm`, `md`, `lg`, `xl`
**状态**: `loading`, `disabled`, `active`

**使用示例**:
```javascript
const button = new Button({
    text: 'Click Me',
    variant: 'primary',
    size: 'md',
    onClick: () => console.log('Button clicked!')
}).mount('#button-container');
```

### Card 组件
灵活的卡片组件，支持多种布局和样式。

**变体**: `default`, `music`, `featured`, `release`, `elevated`, `outlined`

**使用示例**:
```javascript
const musicCard = Card.createMusicCard({
    icon: '🎵',
    title: 'Song Title',
    backgroundColor: '#4CAF50',
    clickable: true,
    onClick: () => console.log('Card clicked!')
}).mount('#card-container');
```

### Navigation 组件
导航组件集合，包括标签导航、底部导航和面包屑。

**组件类型**:
- `TabNavigation` - 标签导航
- `BottomNavigation` - 底部导航
- `Breadcrumb` - 面包屑导航

**使用示例**:
```javascript
const tabNav = new TabNavigation({
    tabs: ['All', 'Music', 'Podcasts'],
    activeTab: 0,
    onChange: (index, tab) => console.log('Tab changed:', tab)
}).mount('#tab-container');
```

### Grid 组件
响应式网格系统，支持多种布局类型。

**类型**: `default`, `music`, `release`, `masonry`
**特性**: 响应式、可配置列数、自定义渲染器

**使用示例**:
```javascript
const grid = Grid.createMusicGrid(items, {
    columns: 2,
    itemRenderer: (item, index) => `<div class="card">${item.title}</div>`
}).mount('#grid-container');
```

## 🚀 快速开始

### 1. 查看组件预览
打开 `playground/index.html` 查看所有组件的完整展示和交互示例。

### 2. 使用组件
1. 引入设计令牌和组件样式：
```html
<link rel="stylesheet" href="design-tokens.css">
<link rel="stylesheet" href="components/ui/button/button.css">
```

2. 引入组件脚本：
```html
<script src="components/ui/button/button.js"></script>
```

3. 创建和使用组件：
```javascript
const button = new Button({
    text: 'Hello World',
    variant: 'primary'
}).mount('#my-container');
```

### 3. 查看完整示例
- `music-app.html` - 原始音乐应用
- `music-app-refactored.html` - 使用设计系统重构后的应用

## 🎯 设计原则

### DRY (Don't Repeat Yourself)
- 所有重复的样式都提取到设计令牌中
- 组件逻辑复用，避免代码重复
- 统一的命名规范和结构

### 组件化
- 每个组件都是独立的、可复用的
- 清晰的 API 和配置选项
- 支持组合和扩展

### 可访问性
- 语义化的 HTML 结构
- 键盘导航支持
- ARIA 属性和角色
- 高对比度模式支持

### 响应式设计
- 移动优先的设计方法
- 灵活的布局系统
- 适配不同屏幕尺寸

## 🛠️ 开发指南

### 添加新组件
1. 在 `components/ui/` 下创建新文件夹
2. 创建 CSS、JS 和示例 HTML 文件
3. 遵循现有的命名规范和结构
4. 在 playground 中添加展示

### 修改设计令牌
1. 编辑 `design-tokens.css`
2. 使用 CSS 自定义属性 (CSS Variables)
3. 确保所有组件都使用令牌而不是硬编码值

### 测试组件
1. 在各个组件的 `index.html` 中测试单个组件
2. 在 playground 中测试组件集成
3. 在重构的音乐应用中测试实际使用场景

## 📱 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🤝 贡献指南

1. 遵循现有的代码风格和结构
2. 确保新组件有完整的文档和示例
3. 测试组件在不同场景下的表现
4. 保持设计系统的一致性

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**设计系统版本**: 1.0.0  
**最后更新**: 2024年12月  
**维护者**: 前端架构师团队
