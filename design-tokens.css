/* Design Tokens - 设计系统基础令牌 */
:root {
  /* Colors - 颜色系统 */
  --color-primary: #30d158;
  --color-primary-hover: #28c946;
  --color-primary-active: #20b534;
  
  --color-secondary: #007aff;
  --color-secondary-hover: #0056cc;
  --color-secondary-active: #004299;
  
  --color-background-primary: #000000;
  --color-background-secondary: #1c1c1e;
  --color-background-tertiary: #2c2c2e;
  --color-background-quaternary: #3a3a3c;
  
  --color-surface-primary: #1c1c1e;
  --color-surface-secondary: #2c2c2e;
  --color-surface-tertiary: #3a3a3c;
  
  --color-text-primary: #ffffff;
  --color-text-secondary: #999999;
  --color-text-tertiary: #666666;
  --color-text-disabled: #444444;
  
  --color-border-primary: #333333;
  --color-border-secondary: #2c2c2e;
  --color-border-tertiary: #1c1c1e;
  
  --color-accent-green: #4CAF50;
  --color-accent-red: #FF5722;
  --color-accent-purple: #9C27B0;
  --color-accent-orange: #FF9800;
  --color-accent-blue: #2196F3;
  --color-accent-teal: #607D8B;
  --color-accent-brown: #795548;
  --color-accent-pink: #E91E63;
  
  /* Typography - 字体系统 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 17px;
  --font-size-2xl: 22px;
  --font-size-3xl: 28px;
  --font-size-4xl: 34px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* Spacing - 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 15px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 30px;
  --spacing-4xl: 40px;
  
  /* Border Radius - 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-full: 50%;
  
  /* Shadows - 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* Transitions - 过渡动画 */
  --transition-fast: 0.1s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-all: all var(--transition-normal);
  
  /* Z-index - 层级系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Layout - 布局系统 */
  --container-max-width: 375px;
  --container-padding: var(--spacing-xl);
  --header-height: 60px;
  --footer-height: 80px;
  --nav-height: 50px;
  
  /* Grid - 网格系统 */
  --grid-columns: 12;
  --grid-gap: var(--spacing-lg);
  --grid-gap-sm: var(--spacing-sm);
  --grid-gap-lg: var(--spacing-xl);
  
  /* Breakpoints - 断点系统 */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  overflow-x: hidden;
}

/* Utility Classes */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }

.bg-primary { background-color: var(--color-background-primary); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-surface { background-color: var(--color-surface-primary); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.transition { transition: var(--transition-all); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.grid { display: grid; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.w-full { width: 100%; }
.h-full { height: 100%; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.cursor-pointer { cursor: pointer; }
.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
