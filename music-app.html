<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: white;
            overflow-x: hidden;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 0;
            position: relative;
            overflow: hidden;
            border: 3px solid #333;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .status-left {
            font-size: 17px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: end;
        }

        .bar {
            width: 3px;
            background: white;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .wifi-icon {
            width: 15px;
            height: 15px;
            background: white;
            border-radius: 50%;
            position: relative;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid white;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: white;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: white;
            border-radius: 1px;
        }

        .nav-tabs {
            display: flex;
            padding: 0 20px;
            gap: 10px;
            margin-bottom: 20px;
        }

        .nav-tab {
            padding: 8px 16px;
            border-radius: 20px;
            background: #333;
            color: #999;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-tab.active {
            background: #30d158;
            color: white;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 200px);
            overflow-y: auto;
        }

        .music-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .music-card {
            background: #1c1c1e;
            border-radius: 12px;
            padding: 12px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .music-card:hover {
            transform: scale(1.02);
        }

        .music-card img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .music-card h3 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .music-card p {
            font-size: 12px;
            color: #999;
            line-height: 1.2;
        }

        .section-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .picked-card {
            background: #1c1c1e;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .picked-card img {
            width: 80px;
            height: 80px;
            border-radius: 8px;
        }

        .picked-content h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .picked-content p {
            font-size: 14px;
            color: #999;
            line-height: 1.4;
        }

        .new-releases {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 20px;
        }

        .release-card {
            min-width: 120px;
            text-align: center;
        }

        .release-card img {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #000;
            padding: 10px 0 30px;
            border-top: 1px solid #333;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            color: #999;
            font-size: 10px;
        }

        .nav-item.active {
            color: white;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            background: currentColor;
            border-radius: 4px;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: white;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">9:41</div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
                <div class="wifi-icon"></div>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab">T</button>
            <button class="nav-tab active">All</button>
            <button class="nav-tab">Music</button>
            <button class="nav-tab">Podcasts</button>
            <button class="nav-tab">Audiobooks</button>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Music Grid -->
            <div class="music-grid">
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #4CAF50; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">🎵</div>
                    <h3>Brat and it's completely diff...</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #FF5722; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">🎭</div>
                    <h3>Wicked Official Playlist</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #9C27B0; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">👤</div>
                    <h3>Gracie Abrams</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #FF9800; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">🎸</div>
                    <h3>More Life</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #2196F3; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">🎧</div>
                    <h3>DJ</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #607D8B; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">🎵</div>
                    <h3>Today's Top Hits</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #795548; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">☀️</div>
                    <h3>eternal sunshine</h3>
                </div>
                <div class="music-card">
                    <div style="width: 40px; height: 40px; background: #E91E63; border-radius: 8px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">💋</div>
                    <h3>Short n' Sweet</h3>
                </div>
            </div>

            <!-- Picked for you -->
            <h2 class="section-title">Picked for you</h2>
            <div class="picked-card">
                <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #FF6B6B, #4ECDC4); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 24px;">🎙️</div>
                <div class="picked-content">
                    <p style="font-size: 12px; color: #999; margin-bottom: 5px;">Podcast</p>
                    <h3>Sounds Like A Cult</h3>
                    <p>A podcast about the modern-day "cults" we all follow.</p>
                </div>
            </div>

            <!-- New releases -->
            <h2 class="section-title">New releases for you</h2>
            <div class="new-releases">
                <div class="release-card">
                    <div style="width: 120px; height: 120px; background: linear-gradient(45deg, #FFD700, #FFA500); border-radius: 8px; margin-bottom: 8px;"></div>
                </div>
                <div class="release-card">
                    <div style="width: 120px; height: 120px; background: linear-gradient(45deg, #00CED1, #1E90FF); border-radius: 8px; margin-bottom: 8px;"></div>
                </div>
                <div class="release-card">
                    <div style="width: 120px; height: 120px; background: linear-gradient(45deg, #DDA0DD, #9370DB); border-radius: 8px; margin-bottom: 8px;"></div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-items">
                <div class="nav-item active">
                    <div class="nav-icon"></div>
                    <span>Home</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Search</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Your Library</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Create</span>
                </div>
            </div>
        </div>

        <!-- Home Indicator -->
        <div class="home-indicator"></div>
    </div>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Music card click effects
        document.querySelectorAll('.music-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1.02)';
                }, 100);
            });
        });

        // Bottom navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
